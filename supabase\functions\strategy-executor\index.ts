import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface StrategyRequest {
  strategyId: string;
  userId: string;
  action: 'start' | 'stop' | 'pause' | 'resume';
  parameters?: Record<string, any>;
}

interface StrategyExecution {
  id: string;
  strategyId: string;
  userId: string;
  status: 'running' | 'stopped' | 'paused' | 'error';
  startTime: number;
  endTime?: number;
  parameters: Record<string, any>;
  performance: {
    totalTrades: number;
    winRate: number;
    totalPnl: number;
    maxDrawdown: number;
  };
  lastSignal?: {
    type: 'buy' | 'sell' | 'hold';
    symbol: string;
    price: number;
    quantity: number;
    timestamp: number;
    confidence: number;
  };
}

interface TradingSignal {
  type: 'buy' | 'sell' | 'hold';
  symbol: string;
  price: number;
  quantity: number;
  stopLoss?: number;
  takeProfit?: number;
  confidence: number;
  reasoning: string;
  timestamp: number;
}

class StrategyExecutor {
  private supabase: any;
  private executions: Map<string, StrategyExecution> = new Map();
  private intervals: Map<string, number> = new Map();

  constructor() {
    this.supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );
  }

  async startStrategy(request: StrategyRequest): Promise<StrategyExecution> {
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Load strategy from database
    const { data: strategy, error } = await this.supabase
      .from('strategies')
      .select('*')
      .eq('id', request.strategyId)
      .eq('user_id', request.userId)
      .single();

    if (error || !strategy) {
      throw new Error(`Strategy not found: ${request.strategyId}`);
    }

    // Validate user permissions and broker connection
    await this.validateUserAccess(request.userId);

    const execution: StrategyExecution = {
      id: executionId,
      strategyId: request.strategyId,
      userId: request.userId,
      status: 'running',
      startTime: Date.now(),
      parameters: { ...strategy.default_parameters, ...request.parameters },
      performance: {
        totalTrades: 0,
        winRate: 0,
        totalPnl: 0,
        maxDrawdown: 0,
      },
    };

    this.executions.set(executionId, execution);

    // Save execution to database
    await this.supabase
      .from('strategy_executions')
      .insert({
        id: executionId,
        strategy_id: request.strategyId,
        user_id: request.userId,
        status: 'running',
        start_time: new Date(execution.startTime).toISOString(),
        parameters: execution.parameters,
        performance: execution.performance,
      });

    // Start execution loop
    await this.startExecutionLoop(execution, strategy);

    return execution;
  }

  async stopStrategy(executionId: string): Promise<void> {
    const execution = this.executions.get(executionId);
    if (!execution) {
      throw new Error(`Execution not found: ${executionId}`);
    }

    execution.status = 'stopped';
    execution.endTime = Date.now();

    // Clear interval
    const intervalId = this.intervals.get(executionId);
    if (intervalId) {
      clearInterval(intervalId);
      this.intervals.delete(executionId);
    }

    // Update database
    await this.supabase
      .from('strategy_executions')
      .update({
        status: 'stopped',
        end_time: new Date(execution.endTime).toISOString(),
        performance: execution.performance,
      })
      .eq('id', executionId);

    this.executions.delete(executionId);
  }

  private async startExecutionLoop(execution: StrategyExecution, strategy: any): Promise<void> {
    const intervalMs = strategy.execution_interval || 60000; // Default 1 minute

    const intervalId = setInterval(async () => {
      try {
        if (execution.status !== 'running') {
          clearInterval(intervalId);
          return;
        }

        await this.executeStrategyIteration(execution, strategy);
      } catch (error) {
        console.error(`Strategy execution error for ${execution.id}:`, error);
        execution.status = 'error';
        
        // Update database with error status
        await this.supabase
          .from('strategy_executions')
          .update({ status: 'error' })
          .eq('id', execution.id);
        
        clearInterval(intervalId);
      }
    }, intervalMs);

    this.intervals.set(execution.id, intervalId);
  }

  private async executeStrategyIteration(execution: StrategyExecution, strategy: any): Promise<void> {
    // Get latest market data
    const marketData = await this.getMarketData(strategy.symbols);
    
    // Execute strategy logic
    const signal = await this.evaluateStrategy(strategy, marketData, execution.parameters);
    
    if (signal && signal.type !== 'hold') {
      // Validate signal
      if (await this.validateSignal(signal, execution.userId)) {
        // Execute trade
        await this.executeTrade(signal, execution);
        
        // Update execution
        execution.lastSignal = signal;
        execution.performance.totalTrades++;
        
        // Save signal to database
        await this.supabase
          .from('trading_signals')
          .insert({
            execution_id: execution.id,
            strategy_id: execution.strategyId,
            user_id: execution.userId,
            type: signal.type,
            symbol: signal.symbol,
            price: signal.price,
            quantity: signal.quantity,
            stop_loss: signal.stopLoss,
            take_profit: signal.takeProfit,
            confidence: signal.confidence,
            reasoning: signal.reasoning,
            timestamp: new Date(signal.timestamp).toISOString(),
          });
      }
    }

    // Update performance metrics
    await this.updatePerformanceMetrics(execution);
    
    // Update database
    await this.supabase
      .from('strategy_executions')
      .update({
        performance: execution.performance,
        last_signal: execution.lastSignal,
        updated_at: new Date().toISOString(),
      })
      .eq('id', execution.id);
  }

  private async getMarketData(symbols: string[]): Promise<any> {
    // Fetch latest market data for symbols
    // This would integrate with your data provider
    const marketData: Record<string, any> = {};
    
    for (const symbol of symbols) {
      // Mock data - replace with real data provider
      marketData[symbol] = {
        price: 100 + Math.random() * 10,
        volume: Math.floor(Math.random() * 1000000),
        timestamp: Date.now(),
      };
    }
    
    return marketData;
  }

  private async evaluateStrategy(strategy: any, marketData: any, parameters: any): Promise<TradingSignal | null> {
    // This is where you'd implement the strategy evaluation logic
    // For now, return a simple random signal for demonstration
    
    const symbols = Object.keys(marketData);
    if (symbols.length === 0) return null;
    
    const symbol = symbols[0];
    const price = marketData[symbol].price;
    
    // Simple random strategy for demonstration
    const random = Math.random();
    if (random > 0.7) {
      return {
        type: 'buy',
        symbol,
        price,
        quantity: parameters.position_size || 100,
        stopLoss: price * 0.98,
        takeProfit: price * 1.05,
        confidence: random,
        reasoning: 'Random buy signal for demonstration',
        timestamp: Date.now(),
      };
    } else if (random < 0.3) {
      return {
        type: 'sell',
        symbol,
        price,
        quantity: parameters.position_size || 100,
        stopLoss: price * 1.02,
        takeProfit: price * 0.95,
        confidence: 1 - random,
        reasoning: 'Random sell signal for demonstration',
        timestamp: Date.now(),
      };
    }
    
    return null; // Hold
  }

  private async validateSignal(signal: TradingSignal, userId: string): Promise<boolean> {
    // Implement signal validation logic
    // Check risk limits, position sizes, etc.
    
    // Get user's risk settings
    const { data: riskSettings } = await this.supabase
      .from('user_risk_settings')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (riskSettings) {
      // Check maximum position size
      if (signal.quantity * signal.price > riskSettings.max_position_value) {
        return false;
      }
      
      // Check daily loss limit
      // Implementation would check current day's PnL
    }
    
    return true;
  }

  private async executeTrade(signal: TradingSignal, execution: StrategyExecution): Promise<void> {
    // This would integrate with the broker client
    // For now, just log the trade
    console.log(`Executing trade for ${execution.userId}:`, signal);
    
    // In a real implementation, you would:
    // 1. Get user's broker client
    // 2. Place the order
    // 3. Handle order confirmation
    // 4. Update positions
  }

  private async updatePerformanceMetrics(execution: StrategyExecution): Promise<void> {
    // Calculate performance metrics
    // This would analyze all trades for this execution
    
    const { data: trades } = await this.supabase
      .from('trading_signals')
      .select('*')
      .eq('execution_id', execution.id);
    
    if (trades && trades.length > 0) {
      // Calculate win rate, PnL, etc.
      // For now, just update trade count
      execution.performance.totalTrades = trades.length;
    }
  }

  private async validateUserAccess(userId: string): Promise<void> {
    // Check if user has active broker connection
    const { data: brokerConfig } = await this.supabase
      .from('user_broker_configs')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (!brokerConfig) {
      throw new Error('No broker configuration found for user');
    }
    
    // Additional validation logic here
  }
}

serve(async (req) => {
  try {
    if (req.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 });
    }

    const request: StrategyRequest = await req.json();
    const executor = new StrategyExecutor();

    let result;
    switch (request.action) {
      case 'start':
        result = await executor.startStrategy(request);
        break;
      case 'stop':
        await executor.stopStrategy(request.strategyId);
        result = { success: true };
        break;
      default:
        throw new Error(`Unknown action: ${request.action}`);
    }

    return new Response(JSON.stringify(result), {
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Strategy executor error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
});
