import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should navigate to login page', async ({ page }) => {
    await page.click('text=Login');
    await expect(page).toHaveURL('/auth/login');
    await expect(page.locator('h1')).toContainText('Sign in');
  });

  test('should show validation errors for empty form', async ({ page }) => {
    await page.goto('/auth/login');
    await page.click('button[type="submit"]');
    
    // Check for validation messages
    await expect(page.locator('text=Email is required')).toBeVisible();
    await expect(page.locator('text=Password is required')).toBeVisible();
  });

  test('should show error for invalid credentials', async ({ page }) => {
    await page.goto('/auth/login');
    
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'wrongpassword');
    await page.click('button[type="submit"]');
    
    await expect(page.locator('text=Invalid credentials')).toBeVisible();
  });

  test('should navigate to signup page', async ({ page }) => {
    await page.goto('/auth/login');
    await page.click('text=Sign up');
    
    await expect(page).toHaveURL('/auth/sign-up');
    await expect(page.locator('h1')).toContainText('Create account');
  });

  test('should show validation errors for signup form', async ({ page }) => {
    await page.goto('/auth/sign-up');
    await page.click('button[type="submit"]');
    
    await expect(page.locator('text=Email is required')).toBeVisible();
    await expect(page.locator('text=Password is required')).toBeVisible();
  });

  test('should validate password strength', async ({ page }) => {
    await page.goto('/auth/sign-up');
    
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', '123');
    
    await expect(page.locator('text=Password must be at least 8 characters')).toBeVisible();
  });

  test('should handle OAuth login buttons', async ({ page }) => {
    await page.goto('/auth/login');
    
    // Check that OAuth buttons are present
    await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();
    await expect(page.locator('button:has-text("Continue with GitHub")')).toBeVisible();
  });

  test('should redirect authenticated users from auth pages', async ({ page, context }) => {
    // Mock authenticated state
    await context.addCookies([
      {
        name: 'sb-access-token',
        value: 'mock-token',
        domain: 'localhost',
        path: '/',
      },
    ]);

    await page.goto('/auth/login');
    
    // Should redirect to charts page
    await expect(page).toHaveURL('/charts');
  });

  test('should protect charts page for unauthenticated users', async ({ page }) => {
    await page.goto('/charts');
    
    // Should redirect to login page
    await expect(page).toHaveURL('/auth/login');
  });
});

test.describe('Password Reset Flow', () => {
  test('should navigate to forgot password page', async ({ page }) => {
    await page.goto('/auth/login');
    await page.click('text=Forgot password?');
    
    await expect(page).toHaveURL('/auth/forgot-password');
    await expect(page.locator('h1')).toContainText('Reset password');
  });

  test('should validate email for password reset', async ({ page }) => {
    await page.goto('/auth/forgot-password');
    await page.click('button[type="submit"]');
    
    await expect(page.locator('text=Email is required')).toBeVisible();
  });

  test('should show success message after password reset request', async ({ page }) => {
    await page.goto('/auth/forgot-password');
    
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.click('button[type="submit"]');
    
    await expect(page.locator('text=Check your email')).toBeVisible();
  });
});
