{"ci": {"collect": {"url": ["http://localhost:3000", "http://localhost:3000/charts"], "startServerCommand": "npm run start", "numberOfRuns": 3}, "assert": {"assertions": {"categories:performance": ["warn", {"minScore": 0.8}], "categories:accessibility": ["error", {"minScore": 0.9}], "categories:best-practices": ["warn", {"minScore": 0.8}], "categories:seo": ["warn", {"minScore": 0.8}], "first-contentful-paint": ["warn", {"maxNumericValue": 2000}], "largest-contentful-paint": ["warn", {"maxNumericValue": 2500}], "cumulative-layout-shift": ["warn", {"maxNumericValue": 0.1}], "total-blocking-time": ["warn", {"maxNumericValue": 300}]}}, "upload": {"target": "temporary-public-storage"}}}