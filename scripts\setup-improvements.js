#!/usr/bin/env node

/**
 * Setup script for trading platform improvements
 * This script installs dependencies and sets up the development environment
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up Trading Platform Improvements...\n');

// Check if we're in the right directory
if (!fs.existsSync('package.json')) {
  console.error('❌ Error: package.json not found. Please run this script from the project root.');
  process.exit(1);
}

// Function to run commands safely
function runCommand(command, description) {
  try {
    console.log(`📦 ${description}...`);
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} completed\n`);
  } catch (error) {
    console.error(`❌ Error during ${description}:`, error.message);
    process.exit(1);
  }
}

// Function to check if command exists
function commandExists(command) {
  try {
    execSync(`which ${command}`, { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

// Check prerequisites
console.log('🔍 Checking prerequisites...');

if (!commandExists('node')) {
  console.error('❌ Node.js is not installed. Please install Node.js 18+ first.');
  process.exit(1);
}

if (!commandExists('npm')) {
  console.error('❌ npm is not installed. Please install npm first.');
  process.exit(1);
}

console.log('✅ Prerequisites check passed\n');

// Install dependencies
console.log('📦 Installing dependencies...');

// Production dependencies
const prodDeps = [
  'zustand@^5.0.2'
];

// Development dependencies
const devDeps = [
  '@playwright/test@^1.48.0',
  '@testing-library/jest-dom@^6.6.3',
  '@testing-library/react@^16.1.0',
  '@testing-library/user-event@^14.5.2',
  '@vitejs/plugin-react@^4.3.4',
  '@vitest/coverage-v8@^2.1.8',
  'jsdom@^25.0.1',
  'prettier@^3.4.2',
  'prettier-plugin-tailwindcss@^0.6.9',
  'vitest@^2.1.8',
  'web-vitals@^4.2.4'
];

// Install production dependencies
if (prodDeps.length > 0) {
  runCommand(`npm install ${prodDeps.join(' ')}`, 'Installing production dependencies');
}

// Install development dependencies
if (devDeps.length > 0) {
  runCommand(`npm install --save-dev ${devDeps.join(' ')}`, 'Installing development dependencies');
}

// Install Playwright browsers
runCommand('npx playwright install', 'Installing Playwright browsers');

// Create necessary directories
console.log('📁 Creating directories...');
const directories = [
  '__tests__',
  'e2e',
  'supabase/functions',
  'lib/stores',
  '.github/workflows'
];

directories.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`✅ Created directory: ${dir}`);
  }
});

// Check if .env file exists and has required variables
console.log('\n🔧 Checking environment configuration...');

if (fs.existsSync('.env')) {
  const envContent = fs.readFileSync('.env', 'utf8');
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];
  
  const missingVars = requiredVars.filter(varName => !envContent.includes(varName));
  
  if (missingVars.length > 0) {
    console.log('⚠️  Missing environment variables:');
    missingVars.forEach(varName => console.log(`   - ${varName}`));
    console.log('\nPlease add these to your .env file.');
  } else {
    console.log('✅ Environment variables configured');
  }
} else {
  console.log('⚠️  .env file not found. Please create one with your Supabase credentials.');
}

// Create .env.example if it doesn't exist
if (!fs.existsSync('.env.example')) {
  const envExample = `# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Development tools configuration
NEXT_PUBLIC_ENABLE_DEVTOOLS=false
NEXT_PUBLIC_DISABLE_REACT_QUERY_DEVTOOLS=true

# Monitoring and Analytics
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn_here
NEXT_PUBLIC_ENABLE_MONITORING=true

# Trading Configuration
NEXT_PUBLIC_ENABLE_PAPER_TRADING=true
NEXT_PUBLIC_DEFAULT_BROKER=alpaca

# Performance Monitoring
NEXT_PUBLIC_PERFORMANCE_BUDGET_FCP=2000
NEXT_PUBLIC_PERFORMANCE_BUDGET_LCP=2500
`;
  
  fs.writeFileSync('.env.example', envExample);
  console.log('✅ Created .env.example file');
}

// Update .gitignore if needed
console.log('\n📝 Updating .gitignore...');
const gitignoreAdditions = [
  '# Testing',
  'coverage/',
  'playwright-report/',
  'test-results/',
  '',
  '# Monitoring',
  'lighthouse-report.html',
  'performance-reports/',
  '',
  '# IDE',
  '.vscode/',
  '.idea/',
  '',
  '# Logs',
  '*.log',
  'logs/',
];

let gitignoreContent = '';
if (fs.existsSync('.gitignore')) {
  gitignoreContent = fs.readFileSync('.gitignore', 'utf8');
}

const newEntries = gitignoreAdditions.filter(entry => 
  entry === '' || !gitignoreContent.includes(entry)
);

if (newEntries.length > 0) {
  fs.appendFileSync('.gitignore', '\n' + newEntries.join('\n') + '\n');
  console.log('✅ Updated .gitignore');
} else {
  console.log('✅ .gitignore is up to date');
}

// Run initial type check
console.log('\n🔍 Running type check...');
try {
  execSync('npx tsc --noEmit', { stdio: 'inherit' });
  console.log('✅ Type check passed');
} catch (error) {
  console.log('⚠️  Type check found issues. Please review and fix them.');
}

// Run initial linting
console.log('\n🔍 Running linter...');
try {
  execSync('npm run lint', { stdio: 'inherit' });
  console.log('✅ Linting passed');
} catch (error) {
  console.log('⚠️  Linting found issues. Run `npm run lint:fix` to auto-fix some issues.');
}

// Final instructions
console.log('\n🎉 Setup completed successfully!\n');
console.log('📋 Next steps:');
console.log('1. Configure your .env file with Supabase credentials');
console.log('2. Run database migrations: `npm run db:migrate` (if you have Supabase CLI)');
console.log('3. Start development server: `npm run dev`');
console.log('4. Run tests: `npm run test`');
console.log('5. Run E2E tests: `npm run test:e2e`');
console.log('\n📚 New features available:');
console.log('- Real-time data with Supabase Realtime');
console.log('- Broker integration layer');
console.log('- Global state management with Zustand');
console.log('- Comprehensive testing with Vitest and Playwright');
console.log('- Performance monitoring and error tracking');
console.log('- CI/CD pipeline with GitHub Actions');
console.log('\n🔗 Useful commands:');
console.log('- `npm run test:coverage` - Run tests with coverage');
console.log('- `npm run test:e2e:ui` - Run E2E tests with UI');
console.log('- `npm run format` - Format code with Prettier');
console.log('- `npm run type-check` - Check TypeScript types');
console.log('\nHappy coding! 🚀');
